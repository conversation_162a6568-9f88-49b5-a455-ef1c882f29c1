"""Add psychopedagogical questionnaire fields

Revision ID: be819b6dbaa5
Revises: 40cd3ab49a5e
Create Date: 2025-08-22 13:28:31.233201

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'be819b6dbaa5'
down_revision: Union[str, None] = '40cd3ab49a5e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cuestionario', sa.Column('tipo_cuestionario', sa.String(), nullable=True))
    op.add_column('cuestionario', sa.Column('respuestas', sa.JSON(), nullable=True))
    op.add_column('cuestionario', sa.Column('reporte_ia', sa.Text(), nullable=True))
    op.add_column('cuestionario', sa.Column('fecha_creacion', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True))
    op.add_column('cuestionario', sa.Column('fecha_completado', sa.DateTime(), nullable=True))
    op.add_column('cuestionario', sa.Column('id_persona', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'cuestionario', 'personas', ['id_persona'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'cuestionario', type_='foreignkey')
    op.drop_column('cuestionario', 'id_persona')
    op.drop_column('cuestionario', 'fecha_completado')
    op.drop_column('cuestionario', 'fecha_creacion')
    op.drop_column('cuestionario', 'reporte_ia')
    op.drop_column('cuestionario', 'respuestas')
    op.drop_column('cuestionario', 'tipo_cuestionario')
    # ### end Alembic commands ###
