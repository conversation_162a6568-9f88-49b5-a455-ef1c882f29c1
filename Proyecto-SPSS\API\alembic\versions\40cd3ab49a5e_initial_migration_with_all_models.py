"""Initial migration with all models

Revision ID: 40cd3ab49a5e
Revises: 
Create Date: 2025-08-22 12:26:04.257410

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '40cd3ab49a5e'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cohorte',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('nombre', sa.String(), nullable=True),
    sa.Column('descripcion', sa.String(), nullable=True),
    sa.Column('fecha_inicio', sa.DateTime(), nullable=True),
    sa.Column('fecha_fin', sa.DateTime(), nullable=True),
    sa.Column('activo', sa.<PERSON>(), nullable=True),
    sa.Column('fecha_creacion', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cohorte_id'), 'cohorte', ['id'], unique=False)
    op.create_index(op.f('ix_cohorte_nombre'), 'cohorte', ['nombre'], unique=True)
    op.create_table('personas',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tipo_persona', sa.String(), nullable=False),
    sa.Column('sexo', sa.String(), nullable=False),
    sa.Column('genero', sa.String(), nullable=False),
    sa.Column('edad', sa.Integer(), nullable=False),
    sa.Column('estado_civil', sa.String(), nullable=False),
    sa.Column('religion', sa.String(), nullable=True),
    sa.Column('trabaja', sa.Boolean(), nullable=True),
    sa.Column('lugar_trabajo', sa.String(), nullable=True),
    sa.Column('lugar_origen', sa.String(), nullable=False),
    sa.Column('colonia_residencia_actual', sa.String(), nullable=False),
    sa.Column('celular', sa.String(), nullable=False),
    sa.Column('correo_institucional', sa.String(), nullable=False),
    sa.Column('discapacidad', sa.String(), nullable=True),
    sa.Column('observaciones', sa.String(), nullable=True),
    sa.Column('matricula', sa.String(), nullable=True),
    sa.Column('semestre', sa.Integer(), nullable=True),
    sa.Column('numero_hijos', sa.Integer(), nullable=True),
    sa.Column('grupo_etnico', sa.String(), nullable=True),
    sa.Column('rol', sa.String(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('hashed_password', sa.String(), nullable=False),
    sa.Column('fecha_creacion', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('fecha_actualizacion', sa.DateTime(), nullable=True),
    sa.Column('cohorte_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['cohorte_id'], ['cohorte.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_personas_correo_institucional'), 'personas', ['correo_institucional'], unique=True)
    op.create_index(op.f('ix_personas_id'), 'personas', ['id'], unique=False)
    op.create_index(op.f('ix_personas_matricula'), 'personas', ['matricula'], unique=True)
    op.drop_index(op.f('ix_persona_correo_institucional'), table_name='persona')
    op.drop_index(op.f('ix_persona_id'), table_name='persona')
    op.drop_index(op.f('ix_persona_matricula'), table_name='persona')
    op.drop_index(op.f('ix_persona_tipo_persona'), table_name='persona')
    op.drop_table('persona')
    op.drop_constraint(None, 'atencion', type_='foreignkey')
    op.create_foreign_key(None, 'atencion', 'personas', ['id_persona'], ['id'])
    op.drop_constraint(None, 'contacto_emergencia', type_='foreignkey')
    op.create_foreign_key(None, 'contacto_emergencia', 'personas', ['id_persona'], ['id'])
    op.add_column('grupo', sa.Column('cohorte_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'grupo', 'cohorte', ['cohorte_id'], ['id'])
    op.drop_column('grupo', 'cohorte')
    op.add_column('persona_grupo', sa.Column('persona_id', sa.Integer(), nullable=False))
    op.add_column('persona_grupo', sa.Column('grupo_id', sa.Integer(), nullable=False))
    op.drop_constraint(None, 'persona_grupo', type_='foreignkey')
    op.drop_constraint(None, 'persona_grupo', type_='foreignkey')
    op.create_foreign_key(None, 'persona_grupo', 'grupo', ['grupo_id'], ['id'])
    op.create_foreign_key(None, 'persona_grupo', 'personas', ['persona_id'], ['id'])
    op.drop_column('persona_grupo', 'id_grupo')
    op.drop_column('persona_grupo', 'id_persona')
    op.add_column('persona_programa', sa.Column('persona_id', sa.Integer(), nullable=False))
    op.add_column('persona_programa', sa.Column('programa_id', sa.Integer(), nullable=False))
    op.drop_constraint(None, 'persona_programa', type_='foreignkey')
    op.drop_constraint(None, 'persona_programa', type_='foreignkey')
    op.create_foreign_key(None, 'persona_programa', 'programa_educativo', ['programa_id'], ['id'])
    op.create_foreign_key(None, 'persona_programa', 'personas', ['persona_id'], ['id'])
    op.drop_column('persona_programa', 'id_programa')
    op.drop_column('persona_programa', 'id_persona')
    op.drop_constraint(None, 'personal', type_='foreignkey')
    op.create_foreign_key(None, 'personal', 'personas', ['id_persona'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'personal', type_='foreignkey')
    op.create_foreign_key(None, 'personal', 'persona', ['id_persona'], ['id'])
    op.add_column('persona_programa', sa.Column('id_persona', sa.INTEGER(), nullable=False))
    op.add_column('persona_programa', sa.Column('id_programa', sa.INTEGER(), nullable=False))
    op.drop_constraint(None, 'persona_programa', type_='foreignkey')
    op.drop_constraint(None, 'persona_programa', type_='foreignkey')
    op.create_foreign_key(None, 'persona_programa', 'persona', ['id_persona'], ['id'])
    op.create_foreign_key(None, 'persona_programa', 'programa_educativo', ['id_programa'], ['id'])
    op.drop_column('persona_programa', 'programa_id')
    op.drop_column('persona_programa', 'persona_id')
    op.add_column('persona_grupo', sa.Column('id_persona', sa.INTEGER(), nullable=False))
    op.add_column('persona_grupo', sa.Column('id_grupo', sa.INTEGER(), nullable=False))
    op.drop_constraint(None, 'persona_grupo', type_='foreignkey')
    op.drop_constraint(None, 'persona_grupo', type_='foreignkey')
    op.create_foreign_key(None, 'persona_grupo', 'persona', ['id_persona'], ['id'])
    op.create_foreign_key(None, 'persona_grupo', 'grupo', ['id_grupo'], ['id'])
    op.drop_column('persona_grupo', 'grupo_id')
    op.drop_column('persona_grupo', 'persona_id')
    op.add_column('grupo', sa.Column('cohorte', sa.VARCHAR(), nullable=True))
    op.drop_constraint(None, 'grupo', type_='foreignkey')
    op.drop_column('grupo', 'cohorte_id')
    op.drop_constraint(None, 'contacto_emergencia', type_='foreignkey')
    op.create_foreign_key(None, 'contacto_emergencia', 'persona', ['id_persona'], ['id'])
    op.drop_constraint(None, 'atencion', type_='foreignkey')
    op.create_foreign_key(None, 'atencion', 'persona', ['id_persona'], ['id'])
    op.create_table('persona',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('tipo_persona', sa.VARCHAR(length=14), nullable=True),
    sa.Column('sexo', sa.VARCHAR(length=9), nullable=True),
    sa.Column('genero', sa.VARCHAR(length=10), nullable=True),
    sa.Column('edad', sa.INTEGER(), nullable=True),
    sa.Column('estado_civil', sa.VARCHAR(length=11), nullable=True),
    sa.Column('religion', sa.VARCHAR(), nullable=True),
    sa.Column('trabaja', sa.BOOLEAN(), nullable=True),
    sa.Column('lugar_trabajo', sa.VARCHAR(), nullable=True),
    sa.Column('lugar_origen', sa.VARCHAR(), nullable=True),
    sa.Column('colonia_residencia_actual', sa.VARCHAR(), nullable=True),
    sa.Column('celular', sa.VARCHAR(), nullable=True),
    sa.Column('correo_institucional', sa.VARCHAR(), nullable=True),
    sa.Column('discapacidad', sa.VARCHAR(), nullable=True),
    sa.Column('observaciones', sa.TEXT(), nullable=True),
    sa.Column('matricula', sa.VARCHAR(), nullable=True),
    sa.Column('semestre', sa.INTEGER(), nullable=True),
    sa.Column('numero_hijos', sa.INTEGER(), nullable=True),
    sa.Column('grupo_etnico', sa.VARCHAR(), nullable=True),
    sa.Column('rol', sa.VARCHAR(length=8), nullable=True),
    sa.Column('hashed_password', sa.VARCHAR(), nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_persona_tipo_persona'), 'persona', ['tipo_persona'], unique=False)
    op.create_index(op.f('ix_persona_matricula'), 'persona', ['matricula'], unique=1)
    op.create_index(op.f('ix_persona_id'), 'persona', ['id'], unique=False)
    op.create_index(op.f('ix_persona_correo_institucional'), 'persona', ['correo_institucional'], unique=1)
    op.drop_index(op.f('ix_personas_matricula'), table_name='personas')
    op.drop_index(op.f('ix_personas_id'), table_name='personas')
    op.drop_index(op.f('ix_personas_correo_institucional'), table_name='personas')
    op.drop_table('personas')
    op.drop_index(op.f('ix_cohorte_nombre'), table_name='cohorte')
    op.drop_index(op.f('ix_cohorte_id'), table_name='cohorte')
    op.drop_table('cohorte')
    # ### end Alembic commands ###
